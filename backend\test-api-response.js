const axios = require('axios');

async function testAPIResponse() {
  try {
    console.log('🔍 Testing API response for business activity...');
    
    const response = await axios.get('http://localhost:5000/api/reports/business-activity', {
      params: {
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        limit: 5
      }
    });

    console.log('📊 API Response Status:', response.status);
    console.log('📊 API Response Data Structure:');
    console.log('- success:', response.data.success);
    console.log('- data keys:', Object.keys(response.data.data || {}));
    
    if (response.data.data?.businessActivity) {
      console.log('\n👥 Sample Business Activity Records:');
      response.data.data.businessActivity.slice(0, 3).forEach((customer, index) => {
        console.log(`${index + 1}. Customer ID: ${customer.customerId}`);
        console.log(`   Customer Name: "${customer.customerName}"`);
        console.log(`   Customer Group: ${customer.customerGroup}`);
        console.log(`   Order Count: ${customer.orderCount}`);
        console.log(`   Revenue: ${customer.revenue}`);
        console.log('   ---');
      });
    }

    console.log('\n✅ API test completed successfully!');
    
  } catch (error) {
    console.error('❌ API Error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testAPIResponse();

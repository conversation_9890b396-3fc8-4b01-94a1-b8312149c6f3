# 🎯 Hoàn Thành 3 Trường Hợp Công Nợ

## ✅ **Yêu cầu đã đáp ứng:**

### **1. Đ<PERSON>n hàng hoàn → Công nợ = 0**
- ✅ Trạng thái `hoan_hang` → `debt_amount = 0`
- ✅ Không tính vào tổng công nợ

### **2. Công nợ thực tế (đã giao thành công)**
- ✅ Chỉ tính đơn hàng `hoan_thanh`
- ✅ Thêm field `actual_debt` trong API

### **3. Công nợ hiện tại (dự tính)**
- ✅ Tính từ đơn hàng: `da_xac_nhan`, `da_dong_goi`, `da_giao`, `hoan_thanh`
- ✅ Loại trừ đơn hàng hoàn

## 🔧 **Logic đã implement:**

### **Định nghĩa trạng thái:**
```javascript
const confirmedStatuses = ['da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh'];
const completedStatuses = ['hoan_thanh']; // Chỉ đơn hàng đã giao thành công
const returnedStatuses = ['hoan_hang']; // Đơn hàng hoàn
```

### **1. Công nợ hiện tại (dự tính):**
```javascript
const currentDebt = orders.reduce((sum, order) => {
  // Đơn hàng hoàn = công nợ 0
  if (returnedStatuses.includes(order.trang_thai)) {
    return sum;
  }
  // Chỉ tính đơn hàng đã xác nhận trở lên
  if (confirmedStatuses.includes(order.trang_thai)) {
    return sum + (parseFloat(order.con_phai_tra) || 0);
  }
  return sum;
}, 0);
```

### **2. Công nợ thực tế:**
```javascript
const actualDebt = orders.reduce((sum, order) => {
  // Đơn hàng hoàn = công nợ 0
  if (returnedStatuses.includes(order.trang_thai)) {
    return sum;
  }
  // Chỉ tính đơn hàng đã giao thành công
  if (completedStatuses.includes(order.trang_thai)) {
    return sum + (parseFloat(order.con_phai_tra) || 0);
  }
  return sum;
}, 0);
```

### **3. Order data format:**
```javascript
debt_amount: returnedStatuses.includes(order.trang_thai) 
  ? 0 // Đơn hàng hoàn = công nợ 0
  : (order.con_phai_tra || 0), // Công nợ gốc
order_status: order.trang_thai,
is_returned: returnedStatuses.includes(order.trang_thai),
is_completed: completedStatuses.includes(order.trang_thai),
```

## 🧪 **Test Results - PERFECT:**

### **Test Case: 3 đơn hàng**
```
1. Đơn xác nhận: con_phai_tra = 300,000đ → Tính vào current
2. Đơn hoàn thành: con_phai_tra = 200,000đ → Tính vào current + actual  
3. Đơn hoàn hàng: con_phai_tra = 150,000đ → Không tính (= 0)
```

### **Kết quả:**
```
✅ Current Debt: 500,000đ (300K + 200K) - ĐÚNG
✅ Actual Debt: 200,000đ (chỉ hoàn thành) - ĐÚNG
✅ Returned Order: debt = 0đ - ĐÚNG
✅ Order Status Tracking: ĐÚNG
```

## 📊 **API Response mới:**

### **GET /api/debt - Debt List:**
```json
{
  "data": [
    {
      "customer_id": 1,
      "customer_name": "Nguyễn Văn A",
      "total_debt": 500000,        // Công nợ hiện tại (dự tính)
      "actual_debt": 200000,       // Công nợ thực tế (đã giao)
      "overdue_debt": 0
    }
  ],
  "stats": {
    "total_debt": 500000,          // Tổng công nợ hiện tại
    "actual_debt": 200000,         // Tổng công nợ thực tế
    "customers_with_debt": 1,
    "customers_with_actual_debt": 1
  }
}
```

### **GET /api/debt/customer/{id} - Customer Detail:**
```json
{
  "customer_id": 1,
  "total_debt": 500000,           // Công nợ hiện tại (dự tính)
  "actual_debt": 200000,          // Công nợ thực tế (đã giao)
  "orders": [
    {
      "order_id": 1,
      "order_status": "da_xac_nhan",
      "debt_amount": 300000,       // con_phai_tra gốc
      "is_returned": false,
      "is_completed": false
    },
    {
      "order_id": 2,
      "order_status": "hoan_thanh",
      "debt_amount": 200000,       // con_phai_tra gốc
      "is_returned": false,
      "is_completed": true
    },
    {
      "order_id": 3,
      "order_status": "hoan_hang",
      "debt_amount": 0,            // Đơn hoàn = 0
      "is_returned": true,
      "is_completed": false
    }
  ]
}
```

## 🎯 **Phân biệt rõ ràng:**

### **1. Công nợ hiện tại (total_debt):**
- **Mục đích:** Dự tính, planning, cash flow
- **Bao gồm:** Tất cả đơn đã xác nhận trở lên
- **Loại trừ:** Đơn hoàn hàng
- **Sử dụng:** Dashboard, báo cáo dự tính

### **2. Công nợ thực tế (actual_debt):**
- **Mục đích:** Thực tế, thu hồi nợ
- **Bao gồm:** Chỉ đơn đã giao thành công
- **Loại trừ:** Đơn hoàn hàng
- **Sử dụng:** Debt collection, báo cáo thực tế

### **3. Đơn hàng hoàn:**
- **Công nợ:** Luôn = 0
- **Hiển thị:** `is_returned: true`
- **Logic:** Không tính vào bất kỳ tổng nào

## 🚀 **Lợi ích:**

### **1. Quản lý chính xác:**
- Phân biệt rõ công nợ dự tính vs thực tế
- Xử lý đúng đơn hàng hoàn
- Tracking trạng thái chi tiết

### **2. Báo cáo đa dạng:**
- Dashboard: Hiển thị cả 2 loại công nợ
- Planning: Sử dụng công nợ hiện tại
- Collection: Sử dụng công nợ thực tế

### **3. Business logic đúng:**
- Đơn hoàn không tạo công nợ
- Chỉ đơn giao thành công mới là nợ thực
- Đơn xác nhận là nợ dự tính

## 🎉 **HOÀN THÀNH:**

### **3 trường hợp đã đáp ứng:**
1. ✅ **Đơn hàng hoàn** → Công nợ = 0
2. ✅ **Công nợ thực tế** → Chỉ đơn giao thành công
3. ✅ **Công nợ hiện tại** → Đơn xác nhận trở lên

### **API hoàn thiện:**
- ✅ **GET /api/debt** - 2 loại công nợ + stats
- ✅ **GET /api/debt/customer/{id}** - Chi tiết đầy đủ
- ✅ **Order tracking** - Trạng thái rõ ràng

### **Test 100% Pass:**
```
✅ Current Debt: 500,000đ (confirmed + completed)
✅ Actual Debt: 200,000đ (completed only)
✅ Returned Order: 0đ (excluded)
✅ Status Tracking: Perfect
```

Hệ thống quản lý công nợ đã hoàn thiện với 3 trường hợp theo yêu cầu! 🎯

'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('payments', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      customer_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'nguoi_dung',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      type: {
        type: Sequelize.ENUM('thu', 'chi'),
        allowNull: false,
        comment: 'thu = thu tiền (giảm công nợ), chi = chi tiền (tăng công nợ)'
      },
      amount: {
        type: Sequelize.DOUBLE,
        allowNull: false
      },
      payment_method: {
        type: Sequelize.ENUM('cash', 'transfer', 'card', 'other'),
        allowNull: false,
        defaultValue: 'cash'
      },
      note: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      is_partial_payment: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'true = thanh toán một phần, false = thanh toán toàn bộ'
      },
      created_by: {
        type: Sequelize.STRING(100),
        allowNull: false
      },
      status: {
        type: Sequelize.ENUM('pending', 'completed', 'cancelled'),
        allowNull: false,
        defaultValue: 'pending'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Thêm indexes
    await queryInterface.addIndex('payments', ['customer_id']);
    await queryInterface.addIndex('payments', ['type']);
    await queryInterface.addIndex('payments', ['status']);
    await queryInterface.addIndex('payments', ['created_at']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('payments');
  }
};

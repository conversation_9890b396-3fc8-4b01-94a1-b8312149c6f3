# SellPro Docker Setup

Hướng dẫn chạy ứng dụng SellPro bằng Docker.

## 📋 Yêu cầu

- Docker Engine 20.10+
- Docker Compose 2.0+
- Make (tù<PERSON> chọn, để sử dụng Makefile)

## 🚀 Khởi động nhanh

### 1. <PERSON><PERSON><PERSON> bị môi trường

```bash
# Copy file cấu hình môi trường
cp backend/.env.example backend/.env

# Chỉnh sửa file .env với thông tin phù hợp
nano backend/.env
```

### 2. Chạy Production

```bash
# Sử dụng Makefile (khuyến nghị)
make prod-build
make prod-up

# Hoặc sử dụng docker-compose trực tiếp
docker-compose build
docker-compose up -d
```

### 3. Chạy Development

```bash
# Sử dụng Makefile
make dev

# Hoặc sử dụng docker-compose trực tiếp
docker-compose -f docker-compose.dev.yml up --build
```

## 🔧 Cấu hình

### Environment Variables (.env)

```env
# Database
DB_HOST=db
DB_PORT=3306
DB_USERNAME=sellpro_user
DB_PASSWORD=your_secure_password
DB_DATABASE=sellpro_db
DB_DIALECT=mysql

# JWT
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d

# Server
PORT=5000
NODE_ENV=production

# Cloudinary (tùy chọn)
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
```

## 📊 Services

### Production (docker-compose.yml)
- **Frontend**: http://localhost:3000 (Nginx + React)
- **Backend**: http://localhost:5000 (Node.js + Express)
- **Database**: localhost:3306 (MySQL 8.0)

### Development (docker-compose.dev.yml)
- **Frontend**: http://localhost:5173 (Vite dev server)
- **Backend**: http://localhost:5000 (Nodemon)
- **Database**: localhost:3307 (MySQL 8.0)

## 🛠️ Makefile Commands

```bash
# Development
make dev          # Khởi động development
make dev-down     # Dừng development
make logs-dev     # Xem logs development

# Production
make prod-build   # Build production images
make prod-up      # Khởi động production
make prod-down    # Dừng production
make logs         # Xem logs production

# Database
make migrate      # Chạy migrations
make seed         # Chạy seeders
make db-reset     # Reset database

# Cleanup
make clean        # Dọn dẹp containers và volumes
make clean-all    # Dọn dẹp toàn bộ
```

## 🗄️ Database Management

### Migrations và Seeders

```bash
# Chạy migrations
docker-compose exec backend npm run migrate

# Chạy seeders
docker-compose exec backend npm run seed

# Rollback migration
docker-compose exec backend npm run migrate:undo
```

### Backup và Restore

```bash
# Backup database
docker-compose exec db mysqldump -u root -p sellpro_db > backup.sql

# Restore database
docker-compose exec -T db mysql -u root -p sellpro_db < backup.sql
```

## 🔍 Troubleshooting

### Kiểm tra logs

```bash
# Tất cả services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f db
```

### Kiểm tra health status

```bash
# Backend health check
curl http://localhost:5000/api/health

# Container status
docker-compose ps
```

### Reset hoàn toàn

```bash
# Dừng tất cả và xóa volumes
docker-compose down -v

# Xóa images
docker-compose down --rmi all

# Dọn dẹp system
docker system prune -af
```

## 🔒 Security Notes

- Thay đổi tất cả passwords mặc định trong production
- Sử dụng JWT secret mạnh
- Cấu hình firewall cho các ports exposed
- Thường xuyên cập nhật base images

## 📝 Development Workflow

1. **Code changes**: Volumes được mount, changes tự động reload
2. **Database changes**: Tạo migrations mới và chạy `make migrate-dev`
3. **Dependencies**: Rebuild containers khi thay đổi package.json
4. **Environment**: Restart containers khi thay đổi .env

## 🚀 Deployment

### Production Checklist

- [ ] Cập nhật .env với production values
- [ ] Set NODE_ENV=production
- [ ] Cấu hình reverse proxy (nginx/traefik)
- [ ] Setup SSL certificates
- [ ] Cấu hình monitoring và logging
- [ ] Setup backup strategy
- [ ] Test health checks

# 🔧 Cập Nhật PaymentModal - Hiển Thị Tiền COD

## ❗ **Yêu cầu:**
Trong trang danh sách đơn hàng, khi cập nhật thanh toán, hiển thị **tiền COD** thay vì **tổng phải trả**.

## ✅ **Những thay đổi đã thực hiện:**

### **1. Thay đổi logic tính toán số tiền mặc định:**

**Trước:**
```javascript
// Sử dụng remainingAmount (còn nợ)
const remainingAmount = (orderData.tong_phai_tra || 0) - (orderData.tong_da_tra || 0);
setPaymentAmount(remainingAmount);
```

**Sau:**
```javascript
// Sử dụng tiền COD
const codAmount = orderData.tien_cod || 0;
setPaymentAmount(codAmount);
```

### **2. <PERSON><PERSON><PERSON> nhật hiển thị thông tin đơn hàng:**

**Trước:**
```
Tổng tiền: 1,000,000đ
Đã thanh toán: 200,000đ  
Còn nợ: 800,000đ
```

**Sau:**
```
Tổng tiền: 1,000,000đ
Tiền COD: 500,000đ  
Còn nợ: 800,000đ
```

### **3. Thay đổi radio button:**

**Trước:**
```
💰 Thanh toán toàn bộ
   800,000đ (remainingAmount)
```

**Sau:**
```
💰 Thanh toán COD
   500,000đ (codAmount)
```

### **4. Cập nhật validation:**

**Trước:**
```javascript
if (values.amount > remainingAmount) {
  message.error('Số tiền thanh toán không được vượt quá số tiền còn nợ');
}
```

**Sau:**
```javascript
if (values.amount > codAmount) {
  message.error('Số tiền thanh toán không được vượt quá số tiền COD');
}
```

### **5. Cập nhật placeholder và max value:**

**Trước:**
```javascript
placeholder="Sẽ tự động điền số tiền còn nợ"
max={remainingAmount}
```

**Sau:**
```javascript
placeholder="Sẽ tự động điền số tiền COD"
max={codAmount}
```

## 🎯 **Kết quả mong đợi:**

### **Khi mở modal thanh toán:**
1. **Số tiền mặc định:** Hiển thị tiền COD thay vì còn nợ
2. **Thông tin đơn hàng:** Hiển thị "Tiền COD" thay vì "Đã thanh toán"
3. **Radio button:** "Thanh toán COD" với số tiền COD
4. **Validation:** Giới hạn theo tiền COD
5. **Input:** Max value = tiền COD

### **Ví dụ cụ thể:**
```
Đơn hàng: DH202507220001
- Tổng tiền: 1,000,000đ
- Tiền COD: 500,000đ
- Còn nợ: 300,000đ

→ Modal sẽ hiển thị:
  💰 Thanh toán COD: 500,000đ
  Số tiền thanh toán: 500,000đ (mặc định)
  Max: 500,000đ
```

## 📁 **Files đã thay đổi:**

### **SaleSysFE/src/components/PaymentModal.jsx:**
- ✅ Line 44: Sử dụng `codAmount` thay vì `remainingAmount`
- ✅ Line 48: Note thanh toán COD
- ✅ Line 62: Thêm biến `codAmount`
- ✅ Line 71: Logic thanh toán full sử dụng COD
- ✅ Line 87: Validation theo COD
- ✅ Line 158: Hiển thị "Tiền COD" thay vì "Đã thanh toán"
- ✅ Line 198: Radio button "Thanh toán COD"
- ✅ Line 206: Hiển thị số tiền COD
- ✅ Line 253: Tối đa theo COD
- ✅ Line 265: Validation rule theo COD
- ✅ Line 283: Placeholder COD
- ✅ Line 287: Max value COD

## 🧪 **Cách test:**

### **Test Case 1: Đơn hàng có COD**
1. Vào trang Orders List
2. Tìm đơn hàng có `tien_cod > 0`
3. Click "Cập nhật thanh toán"
4. ✅ **Kiểm tra:** Modal hiển thị tiền COD đúng
5. ✅ **Kiểm tra:** Số tiền mặc định = tiền COD
6. ✅ **Kiểm tra:** Max value = tiền COD

### **Test Case 2: Đơn hàng không có COD**
1. Tìm đơn hàng có `tien_cod = 0`
2. Click "Cập nhật thanh toán"
3. ✅ **Kiểm tra:** Modal hiển thị "Tiền COD: 0đ"
4. ✅ **Kiểm tra:** Số tiền mặc định = 0
5. ✅ **Kiểm tra:** Max value = 0

### **Test Case 3: Validation**
1. Mở modal với đơn hàng có COD = 500,000đ
2. Thử nhập số tiền > 500,000đ
3. ✅ **Kiểm tra:** Hiển thị lỗi "không được vượt quá số tiền COD"

## 🎉 **Tổng kết:**

PaymentModal đã được cập nhật để:
- ✅ **Hiển thị tiền COD** thay vì tổng phải trả
- ✅ **Sử dụng COD làm số tiền mặc định** cho thanh toán
- ✅ **Validation theo tiền COD**
- ✅ **UI rõ ràng và trực quan**

Bây giờ khi cập nhật thanh toán, người dùng sẽ thấy và thanh toán theo số tiền COD thay vì tổng phải trả! 🚀

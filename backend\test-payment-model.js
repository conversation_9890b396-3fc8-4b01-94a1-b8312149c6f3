const models = require('./models');

async function testPaymentModel() {
  try {
    console.log('🔍 Testing Payment model...');
    console.log('Available models:', Object.keys(models));
    
    const { Payment } = models;
    
    if (!Payment) {
      console.log('❌ Payment model not found');
      return;
    }
    
    console.log('✅ Payment model found');
    console.log('Payment model name:', Payment.name);
    console.log('Payment table name:', Payment.tableName);
    
    // Try to query
    const payments = await Payment.findAll({
      limit: 5
    });
    
    console.log(`📊 Found ${payments.length} payments`);
    console.log('Sample payment:', payments[0] ? payments[0].toJSON() : 'No payments found');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error testing Payment model:', error);
    process.exit(1);
  }
}

testPaymentModel();

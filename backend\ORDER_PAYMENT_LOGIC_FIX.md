# 🔧 Sửa Logic Thanh Toán Đơn Hàng

## ❗ **Vấn đề:**
Logic tạo đơn hàng đang tính sai các trường thanh toán:
- `tong_da_tra` = tiền cọc + tiền COD (SAI)
- `tong_phai_tra` = tổng tiền sản phẩm (SAI)
- `con_phai_tra` = tong_phai_tra - tong_da_tra (SAI)

## ✅ **Logic mới (ĐÚNG):**

### **1. <PERSON><PERSON><PERSON> nghĩa các trường:**
- **`tong_tien`** = Tổng tiền sản phẩm (sau giảm giá)
- **`tong_phai_tra`** = Tiền COD (số tiền cần thu từ khách)
- **`tong_da_tra`** = 0 (ban đầu, chỉ cập nhật khi thanh to<PERSON> thự<PERSON> tế)
- **`con_phai_tra`** = Công nợ (tiền sản phẩm - tiền COD - tiền cọc)

### **2. <PERSON><PERSON><PERSON> thức tính:**
```javascript
// Tổng tiền sản phẩm (sau giảm giá)
const total_product_amount = is_manual_total ? manual_total_amount : (calculated_total - chiet_khau_amount);

// 1. tong_phai_tra = tiền COD
const tong_phai_tra = tien_cod;

// 2. tong_da_tra = 0 (ban đầu)
const tong_da_tra = 0;

// 3. con_phai_tra = công nợ
const con_phai_tra = total_product_amount - tien_cod - tien_coc;
```

### **3. Ý nghĩa công nợ:**
- **con_phai_tra > 0:** Khách nợ mình
- **con_phai_tra < 0:** Mình nợ khách  
- **con_phai_tra = 0:** Đã cân bằng

## 🔧 **Những thay đổi đã thực hiện:**

### **File: SaleSysBE/controllers/orderController.js**

#### **1. Logic tính toán mới (Line 325-342):**
```javascript
// LOGIC MỚI theo yêu cầu:
// 1. tong_phai_tra = tiền COD (số tiền cần thu từ khách)
const tong_phai_tra = tien_cod;

// 2. tong_da_tra = 0 (ban đầu chưa thanh toán gì, chỉ cập nhật khi có thanh toán thực tế)
const tong_da_tra = 0;

// 3. con_phai_tra = công nợ (tiền sản phẩm - tiền COD - tiền cọc)
// Nếu âm = mình nợ khách, nếu dương = khách nợ mình
const con_phai_tra = total_product_amount - tien_cod - tien_coc;
```

#### **2. Cập nhật tạo đơn hàng (Line 412-416):**
```javascript
tong_tien: total_product_amount, // Tổng tiền sản phẩm (sau giảm giá)
chiet_khau: chiet_khau_amount, // Chiết khấu
tong_phai_tra: tong_phai_tra, // = tiền COD (số tiền cần thu từ khách)
tong_da_tra: tong_da_tra, // = 0 (ban đầu chưa thanh toán)
con_phai_tra: con_phai_tra, // = công nợ (tiền SP - COD - cọc)
```

#### **3. Sửa payment_summary (Line 540-550):**
```javascript
payment_summary: {
  product_total: total_product_amount, // Tổng tiền sản phẩm (sau giảm giá)
  deposit: tien_coc,
  cod_amount: tien_cod,
  debt: calculated_debt,
  // ...
}
```

### **File: SaleSysFE/src/pages/Orders/OrderEdit.jsx**

#### **Sửa logic cập nhật đơn hàng (Line 106-111):**
```javascript
const orderData = {
  ...values,
  san_pham_list: products,
  tong_tien: calculateTotal() - (values.chiet_khau || 0), // Tổng tiền sản phẩm (sau giảm giá)
  tong_phai_tra: values.tien_cod || 0 // Tổng phải trả = tiền COD
};
```

## 📊 **Ví dụ cụ thể:**

### **Trường hợp 1: Đơn hàng bình thường**
```
Sản phẩm: 1,000,000đ
Giảm giá: 100,000đ
Tiền cọc: 200,000đ
Tiền COD: 500,000đ

→ Kết quả:
- tong_tien: 900,000đ (1,000,000 - 100,000)
- tong_phai_tra: 500,000đ (= tiền COD)
- tong_da_tra: 0đ (ban đầu)
- con_phai_tra: 200,000đ (900,000 - 500,000 - 200,000)
→ Khách nợ mình 200,000đ
```

### **Trường hợp 2: CTV bán giá cao**
```
Sản phẩm: 1,000,000đ
Giảm giá: 0đ
Tiền cọc: 300,000đ
Tiền COD: 800,000đ

→ Kết quả:
- tong_tien: 1,000,000đ
- tong_phai_tra: 800,000đ (= tiền COD)
- tong_da_tra: 0đ (ban đầu)
- con_phai_tra: -100,000đ (1,000,000 - 800,000 - 300,000)
→ Mình nợ khách 100,000đ (CTV bán được giá cao)
```

### **Trường hợp 3: Đã cân bằng**
```
Sản phẩm: 1,000,000đ
Giảm giá: 0đ
Tiền cọc: 400,000đ
Tiền COD: 600,000đ

→ Kết quả:
- tong_tien: 1,000,000đ
- tong_phai_tra: 600,000đ (= tiền COD)
- tong_da_tra: 0đ (ban đầu)
- con_phai_tra: 0đ (1,000,000 - 600,000 - 400,000)
→ Đã cân bằng
```

## 🧪 **Cách test:**

### **Test Case 1: Tạo đơn hàng mới**
1. Tạo đơn hàng với:
   - Sản phẩm: 1,000,000đ
   - Tiền cọc: 200,000đ
   - Tiền COD: 500,000đ
2. ✅ **Kiểm tra:** `tong_phai_tra` = 500,000đ
3. ✅ **Kiểm tra:** `tong_da_tra` = 0đ
4. ✅ **Kiểm tra:** `con_phai_tra` = 300,000đ

### **Test Case 2: Cập nhật thanh toán**
1. Tạo payment cho đơn hàng trên: 300,000đ
2. ✅ **Kiểm tra:** `tong_da_tra` = 300,000đ
3. ✅ **Kiểm tra:** `con_phai_tra` vẫn = 300,000đ (không đổi)

### **Test Case 3: Kiểm tra công nợ**
1. Tạo đơn hàng với COD > tiền sản phẩm
2. ✅ **Kiểm tra:** `con_phai_tra` < 0 (mình nợ khách)
3. ✅ **Kiểm tra:** Log hiển thị "Mình nợ khách"

## 🎯 **Kết quả mong đợi:**

### **Sau khi sửa:**
1. ✅ **`tong_phai_tra`** = tiền COD (đúng)
2. ✅ **`tong_da_tra`** = 0 ban đầu (đúng)
3. ✅ **`con_phai_tra`** = công nợ thực tế (đúng)
4. ✅ **PaymentModal** hiển thị tiền COD (đúng)
5. ✅ **Debt calculation** chính xác (đúng)

### **Trước khi sửa (SAI):**
1. ❌ **`tong_phai_tra`** = tổng tiền sản phẩm
2. ❌ **`tong_da_tra`** = tiền cọc + tiền COD
3. ❌ **`con_phai_tra`** = tong_phai_tra - tong_da_tra
4. ❌ **PaymentModal** hiển thị tổng phải trả
5. ❌ **Debt calculation** sai

## 🚀 **Tổng kết:**

Logic thanh toán đơn hàng đã được sửa để:
- ✅ **Tách biệt rõ ràng** giữa tiền sản phẩm, tiền COD, và công nợ
- ✅ **`tong_da_tra` = 0`** ban đầu, chỉ cập nhật khi thanh toán thực tế
- ✅ **`tong_phai_tra` = tiền COD`** (số tiền cần thu)
- ✅ **`con_phai_tra` = công nợ`** (tính chính xác)
- ✅ **PaymentModal** hiển thị đúng tiền COD
- ✅ **Debt management** hoạt động chính xác

Bây giờ hệ thống sẽ tính toán và hiển thị thanh toán đúng theo yêu cầu! 🎉

require('dotenv').config();
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Payment, CongNoNguoiDung, sequelize } = require('./models');

async function testPaymentLogicFix() {
  try {
    console.log('🧪 Testing Payment Logic Fix - tong_da_tra >= tong_phai_tra...\n');

    // 1. Tạo khách hàng test
    console.log('1️⃣ Creating test customer...');
    const customer = await NguoiDung.create({
      ho_ten: 'Nguyễn Văn Test Logic',
      so_dien_thoai: '0987654666',
      email: '<EMAIL>',
      loai_nguoi_dung: 'khach_hang',
      trang_thai: 'dang_giao_dich'
    });
    console.log(`✅ Created customer: ${customer.ho_ten} (ID: ${customer.id})`);

    // 2. Tạo đơn hàng test case
    console.log('\n2️⃣ Creating test order...');
    const order = await DonHang.create({
      ma_don_hang: `LOGIC-TEST-${Date.now()}`,
      khach_hang_id: customer.id,
      ngay_ban: new Date(),
      trang_thai: 'da_xac_nhan',
      tong_tien: 1000000, // Tổng tiền sản phẩm
      tong_phai_tra: 500000, // Tổng phải trả (tiền COD)
      tong_da_tra: 0, // Chưa thanh toán
      con_phai_tra: 300000, // Công nợ (tong_tien - tong_phai_tra - tien_coc)
      tien_cod: 500000, // Tiền COD
      tien_coc: 200000, // Tiền cọc
      ghi_chu: 'Test order for payment logic'
    });
    console.log(`✅ Created order: ${order.ma_don_hang}`);
    console.log(`   - tong_tien: ${order.tong_tien?.toLocaleString('vi-VN')}đ`);
    console.log(`   - tong_phai_tra: ${order.tong_phai_tra?.toLocaleString('vi-VN')}đ`);
    console.log(`   - tong_da_tra: ${order.tong_da_tra?.toLocaleString('vi-VN')}đ`);
    console.log(`   - tien_cod: ${order.tien_cod?.toLocaleString('vi-VN')}đ`);
    console.log(`   - tien_coc: ${order.tien_coc?.toLocaleString('vi-VN')}đ`);

    // 3. Test case 1: Thanh toán một phần
    console.log('\n3️⃣ Test Case 1: Partial Payment (300,000đ)...');
    
    // Simulate frontend logic
    let totalAmount = order.tong_phai_tra || 0;
    let paidAmount = order.tong_da_tra || 0;
    let remainingAmount = totalAmount - paidAmount;
    
    console.log('📊 Before Payment:');
    console.log(`   - tong_phai_tra: ${totalAmount.toLocaleString('vi-VN')}đ`);
    console.log(`   - tong_da_tra: ${paidAmount.toLocaleString('vi-VN')}đ`);
    console.log(`   - remainingAmount: ${remainingAmount.toLocaleString('vi-VN')}đ`);
    console.log(`   - isFullyPaid: ${paidAmount >= totalAmount}`);
    console.log(`   - canPay: ${remainingAmount > 0}`);

    // Simulate payment
    const payment1 = 300000;
    await order.update({
      tong_da_tra: paidAmount + payment1,
    });

    // Check after payment
    const updatedOrder1 = await DonHang.findByPk(order.id);
    totalAmount = updatedOrder1.tong_phai_tra || 0;
    paidAmount = updatedOrder1.tong_da_tra || 0;
    remainingAmount = totalAmount - paidAmount;

    console.log('📊 After Payment 1:');
    console.log(`   - tong_phai_tra: ${totalAmount.toLocaleString('vi-VN')}đ`);
    console.log(`   - tong_da_tra: ${paidAmount.toLocaleString('vi-VN')}đ`);
    console.log(`   - remainingAmount: ${remainingAmount.toLocaleString('vi-VN')}đ`);
    console.log(`   - isFullyPaid: ${paidAmount >= totalAmount}`);
    console.log(`   - canPay: ${remainingAmount > 0}`);
    console.log(`   - Frontend Display: "${paidAmount.toLocaleString('vi-VN')}đ | Còn: ${remainingAmount.toLocaleString('vi-VN')}đ"`);

    // 4. Test case 2: Thanh toán đủ
    console.log('\n4️⃣ Test Case 2: Full Payment (200,000đ more)...');
    
    const payment2 = 200000;
    await updatedOrder1.update({
      tong_da_tra: paidAmount + payment2,
    });

    // Check after full payment
    const updatedOrder2 = await DonHang.findByPk(order.id);
    totalAmount = updatedOrder2.tong_phai_tra || 0;
    paidAmount = updatedOrder2.tong_da_tra || 0;
    remainingAmount = totalAmount - paidAmount;

    console.log('📊 After Payment 2 (Full):');
    console.log(`   - tong_phai_tra: ${totalAmount.toLocaleString('vi-VN')}đ`);
    console.log(`   - tong_da_tra: ${paidAmount.toLocaleString('vi-VN')}đ`);
    console.log(`   - remainingAmount: ${remainingAmount.toLocaleString('vi-VN')}đ`);
    console.log(`   - isFullyPaid: ${paidAmount >= totalAmount}`);
    console.log(`   - canPay: ${remainingAmount > 0}`);
    console.log(`   - Frontend Display: "${paidAmount.toLocaleString('vi-VN')}đ | ${remainingAmount <= 0 ? 'Đã thanh toán đủ' : `Còn: ${remainingAmount.toLocaleString('vi-VN')}đ`}"`);

    // 5. Test case 3: Thanh toán thừa
    console.log('\n5️⃣ Test Case 3: Overpayment (100,000đ more)...');
    
    const payment3 = 100000;
    await updatedOrder2.update({
      tong_da_tra: paidAmount + payment3,
    });

    // Check after overpayment
    const updatedOrder3 = await DonHang.findByPk(order.id);
    totalAmount = updatedOrder3.tong_phai_tra || 0;
    paidAmount = updatedOrder3.tong_da_tra || 0;
    remainingAmount = totalAmount - paidAmount;

    console.log('📊 After Payment 3 (Overpaid):');
    console.log(`   - tong_phai_tra: ${totalAmount.toLocaleString('vi-VN')}đ`);
    console.log(`   - tong_da_tra: ${paidAmount.toLocaleString('vi-VN')}đ`);
    console.log(`   - remainingAmount: ${remainingAmount.toLocaleString('vi-VN')}đ`);
    console.log(`   - isFullyPaid: ${paidAmount >= totalAmount}`);
    console.log(`   - canPay: ${remainingAmount > 0}`);
    console.log(`   - Frontend Display: "${paidAmount.toLocaleString('vi-VN')}đ | ${remainingAmount <= 0 ? 'Đã thanh toán đủ' : `Còn: ${remainingAmount.toLocaleString('vi-VN')}đ`}"`);

    // 6. Test handlePayment logic
    console.log('\n6️⃣ Testing handlePayment logic...');
    
    const testCases = [
      { tong_phai_tra: 500000, tong_da_tra: 300000, expected: false, desc: "Partial payment" },
      { tong_phai_tra: 500000, tong_da_tra: 500000, expected: true, desc: "Full payment" },
      { tong_phai_tra: 500000, tong_da_tra: 600000, expected: true, desc: "Overpayment" },
      { tong_phai_tra: 0, tong_da_tra: 0, expected: true, desc: "No payment needed" },
    ];

    testCases.forEach((testCase, index) => {
      const isFullyPaid = testCase.tong_da_tra >= testCase.tong_phai_tra;
      const result = isFullyPaid === testCase.expected ? '✅ PASS' : '❌ FAIL';
      
      console.log(`   Test ${index + 1} (${testCase.desc}): ${result}`);
      console.log(`     - tong_phai_tra: ${testCase.tong_phai_tra.toLocaleString('vi-VN')}đ`);
      console.log(`     - tong_da_tra: ${testCase.tong_da_tra.toLocaleString('vi-VN')}đ`);
      console.log(`     - isFullyPaid: ${isFullyPaid} (expected: ${testCase.expected})`);
    });

    // 7. Test PaymentModal max amount logic
    console.log('\n7️⃣ Testing PaymentModal max amount logic...');
    
    const modalTestCases = [
      { tong_phai_tra: 500000, tong_da_tra: 300000, expectedMax: 200000, desc: "Partial paid" },
      { tong_phai_tra: 500000, tong_da_tra: 500000, expectedMax: 0, desc: "Fully paid" },
      { tong_phai_tra: 500000, tong_da_tra: 600000, expectedMax: 0, desc: "Overpaid" },
    ];

    modalTestCases.forEach((testCase, index) => {
      const remainingAmount = Math.max(0, testCase.tong_phai_tra - testCase.tong_da_tra);
      const result = remainingAmount === testCase.expectedMax ? '✅ PASS' : '❌ FAIL';
      
      console.log(`   Modal Test ${index + 1} (${testCase.desc}): ${result}`);
      console.log(`     - Max amount: ${remainingAmount.toLocaleString('vi-VN')}đ (expected: ${testCase.expectedMax.toLocaleString('vi-VN')}đ)`);
    });

    // 8. Cleanup
    console.log('\n8️⃣ Cleaning up...');
    await updatedOrder3.destroy();
    await customer.destroy();
    console.log('✅ Cleanup completed');

    // 9. Final verification
    console.log('\n9️⃣ Final Verification:');
    console.log('🎯 New Logic Summary:');
    console.log('   - ✅ Payment check: tong_da_tra >= tong_phai_tra');
    console.log('   - ✅ Remaining amount: tong_phai_tra - tong_da_tra');
    console.log('   - ✅ Can pay: remainingAmount > 0');
    console.log('   - ✅ Display: "XXXđ | Còn: YYYđ" or "XXXđ | Đã thanh toán đủ"');
    console.log('   - ✅ PaymentModal max: Math.max(0, tong_phai_tra - tong_da_tra)');
    
    console.log('\n🎉 All payment logic tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await sequelize.close();
  }
}

// Chạy test
testPaymentLogicFix();

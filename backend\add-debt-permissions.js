const { exec } = require('child_process');
const path = require('path');

console.log('🚀 Adding debt management permissions...');

// Chạy seeder để thêm permissions công nợ
exec('npx sequelize-cli db:seed --seed 20250708000001-add-debt-permissions.js', 
  { cwd: __dirname }, 
  (error, stdout, stderr) => {
    if (error) {
      console.error('❌ Error running seeder:', error);
      return;
    }
    
    if (stderr) {
      console.error('⚠️ Seeder stderr:', stderr);
    }
    
    console.log('📋 Seeder output:', stdout);
    console.log('✅ Debt permissions added successfully!');
    console.log('');
    console.log('📝 Added permissions:');
    console.log('  - XEM_CONG_NO: Xem công nợ');
    console.log('  - SUA_CONG_NO: Sửa công nợ');
    console.log('  - TAO_PHIEU_THU: Tạo phiếu thu');
    console.log('  - TAO_PHIEU_CHI: Tạo phiếu chi');
    console.log('  - XEM_BAO_CAO_CONG_NO: Xem báo cáo công nợ');
    console.log('');
    console.log('👤 Assigned to roles:');
    console.log('  - ADMIN: All debt permissions');
    console.log('  - QUAN_LY: XEM_CONG_NO, TAO_PHIEU_THU, XEM_BAO_CAO_CONG_NO');
    console.log('');
    console.log('🔄 Please restart your frontend to see the Accounting menu!');
  }
);

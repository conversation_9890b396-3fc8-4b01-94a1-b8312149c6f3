# 🎯 Công Nợ Thực Tế - Bỏ Phụ Thuộc Trạng Thái

## ✅ **Yêu cầu đã đáp ứng:**

### **🎯 Thay đổi chính:**
- **Bỏ điều kiện trạng thái đơn hàng** trong tính toán công nợ thực tế
- **Áp dụng công thức thuần túy:** `MAX(0, tong_tien - tong_da_tra - tien_coc)`
- **Tính cho TẤT CẢ đơn hàng** bất kể trạng thái

## 🔧 **Chi tiết thay đổi:**

### **File: SaleSysBE/controllers/debtController.js**

#### **1. Sửa logic trong getDebtList (Line 108-115):**

**Trước (CÓ ĐIỀU KIỆN TRẠNG THÁI):**
```javascript
// 2. Công nợ thực tế - tính theo công thức: tiền SP - tiền đã trả - tiền cọc
const actualDebt = orders.reduce((sum, order) => {
  // Đơn hàng hoàn = công nợ 0
  if (returnedStatuses.includes(order.trang_thai)) {
    return sum;
  }
  // Chỉ tính đơn hàng đã giao thành công
  if (completedStatuses.includes(order.trang_thai)) {
    const productAmount = parseFloat(order.tong_tien) || 0;
    const paidAmount = parseFloat(order.tong_da_tra) || 0;
    const depositAmount = parseFloat(order.tien_coc) || 0;
    const orderActualDebt = productAmount - paidAmount - depositAmount;
    return sum + Math.max(0, orderActualDebt);
  }
  return sum;
}, 0);
```

**Sau (KHÔNG ĐIỀU KIỆN TRẠNG THÁI):**
```javascript
// 2. Công nợ thực tế - tính theo công thức: tiền SP - tiền đã trả - tiền cọc (không phụ thuộc trạng thái)
const actualDebt = orders.reduce((sum, order) => {
  const productAmount = parseFloat(order.tong_tien) || 0; // Tiền sản phẩm
  const paidAmount = parseFloat(order.tong_da_tra) || 0; // Tiền đã trả
  const depositAmount = parseFloat(order.tien_coc) || 0; // Tiền cọc
  const orderActualDebt = productAmount - paidAmount - depositAmount;
  return sum + Math.max(0, orderActualDebt); // Không cho phép âm
}, 0);
```

#### **2. Sửa logic trong getCustomerDebtDetail (Line 491-498):**

**Trước (CÓ ĐIỀU KIỆN TRẠNG THÁI):**
```javascript
// 2. Công nợ thực tế - tính theo công thức: tiền SP - tiền đã trả - tiền cọc
const actualDebt = orders.reduce((sum, order) => {
  // Đơn hàng hoàn = công nợ 0
  if (returnedStatuses.includes(order.trang_thai)) {
    return sum;
  }
  // Chỉ tính đơn hàng đã giao thành công
  if (completedStatuses.includes(order.trang_thai)) {
    const productAmount = parseFloat(order.tong_tien) || 0;
    const paidAmount = parseFloat(order.tong_da_tra) || 0;
    const depositAmount = parseFloat(order.tien_coc) || 0;
    const orderActualDebt = productAmount - paidAmount - depositAmount;
    return sum + Math.max(0, orderActualDebt);
  }
  return sum;
}, 0);
```

**Sau (KHÔNG ĐIỀU KIỆN TRẠNG THÁI):**
```javascript
// 2. Công nợ thực tế - tính theo công thức: tiền SP - tiền đã trả - tiền cọc (không phụ thuộc trạng thái)
const actualDebt = orders.reduce((sum, order) => {
  const productAmount = parseFloat(order.tong_tien) || 0; // Tiền sản phẩm
  const paidAmount = parseFloat(order.tong_da_tra) || 0; // Tiền đã trả
  const depositAmount = parseFloat(order.tien_coc) || 0; // Tiền cọc
  const orderActualDebt = productAmount - paidAmount - depositAmount;
  return sum + Math.max(0, orderActualDebt); // Không cho phép âm
}, 0);
```

## 📊 **Logic mới:**

### **Công thức đơn giản:**
```
actual_debt = SUM(MAX(0, tong_tien - tong_da_tra - tien_coc))
```

### **Áp dụng cho TẤT CẢ đơn hàng:**
- ✅ **Draft** (nháp) → Tính theo công thức
- ✅ **Đã xác nhận** → Tính theo công thức  
- ✅ **Đã đóng gói** → Tính theo công thức
- ✅ **Đã giao** → Tính theo công thức
- ✅ **Hoàn thành** → Tính theo công thức
- ✅ **Hoàn hàng** → Tính theo công thức
- ✅ **Hủy** → Tính theo công thức

### **Không có ngoại lệ:**
- Không có `if (returnedStatuses.includes(...))`
- Không có `if (completedStatuses.includes(...))`
- Chỉ có công thức toán học thuần túy

## 🎯 **Ví dụ tính toán:**

### **Case 1: Đơn nháp**
```
Trạng thái: 'draft'
tong_tien: 1,000,000đ
tong_da_tra: 0đ
tien_coc: 200,000đ

Trước: 0đ (không tính vì chưa hoàn thành)
Sau: 800,000đ (1,000,000 - 0 - 200,000)
```

### **Case 2: Đơn hoàn hàng**
```
Trạng thái: 'hoan_hang'
tong_tien: 700,000đ
tong_da_tra: 0đ
tien_coc: 100,000đ

Trước: 0đ (loại trừ vì hoàn hàng)
Sau: 600,000đ (700,000 - 0 - 100,000)
```

### **Case 3: Đơn hủy**
```
Trạng thái: 'huy'
tong_tien: 400,000đ
tong_da_tra: 50,000đ
tien_coc: 100,000đ

Trước: 0đ (không tính vì đã hủy)
Sau: 250,000đ (400,000 - 50,000 - 100,000)
```

### **Case 4: Thanh toán thừa**
```
Trạng thái: 'hoan_thanh'
tong_tien: 500,000đ
tong_da_tra: 600,000đ (trả thừa)
tien_coc: 100,000đ

Công thức: 500,000 - 600,000 - 100,000 = -200,000
Kết quả: MAX(0, -200,000) = 0đ
```

## 🚀 **Lợi ích:**

### **1. Đơn giản hóa:**
- ✅ **Không phức tạp** với điều kiện trạng thái
- ✅ **Công thức duy nhất** cho tất cả đơn hàng
- ✅ **Dễ hiểu** và dễ maintain

### **2. Nhất quán:**
- ✅ **Không phụ thuộc** vào logic trạng thái
- ✅ **Kết quả ổn định** khi trạng thái thay đổi
- ✅ **Phản ánh thực tế** tài chính

### **3. Linh hoạt:**
- ✅ **Hoạt động** với mọi trạng thái đơn hàng
- ✅ **Không cần update** khi thêm trạng thái mới
- ✅ **Logic business** rõ ràng

## 🎨 **Frontend Impact:**

### **Cột "Công nợ thực tế" sẽ hiển thị:**
```
Trước:
- Đơn nháp: 0đ (không tính)
- Đơn hoàn: 0đ (loại trừ)
- Đơn hủy: 0đ (không tính)

Sau:
- Đơn nháp: 800,000đ (tính theo công thức)
- Đơn hoàn: 600,000đ (tính theo công thức)
- Đơn hủy: 250,000đ (tính theo công thức)
```

### **Màu sắc không đổi:**
```javascript
const isZero = (value || 0) === 0;
color: isZero ? "#52c41a" : "#fa8c16"

// 0đ → Xanh lá (không nợ)
// >0đ → Cam (còn nợ thực tế)
```

## 🔄 **Business Logic:**

### **Ý nghĩa mới:**
- **Công nợ thực tế** = Số tiền khách hàng thực sự còn nợ dựa trên:
  - Giá trị hàng hóa đã nhận (`tong_tien`)
  - Số tiền đã thanh toán (`tong_da_tra`)
  - Số tiền cọc đã đặt (`tien_coc`)

### **Không phụ thuộc trạng thái vì:**
- Trạng thái đơn hàng là **workflow**, không phải **tài chính**
- Công nợ là **số liệu tài chính**, không phải **trạng thái xử lý**
- Khách hàng có thể nợ tiền ngay cả khi đơn hàng ở trạng thái bất kỳ

### **Ví dụ thực tế:**
```
Khách đặt hàng 1M, cọc 200K → Nợ 800K (ngay cả khi đơn còn nháp)
Khách hoàn hàng nhưng chưa trả tiền → Vẫn nợ tiền hàng
Khách hủy đơn nhưng đã nhận hàng → Vẫn phải trả tiền
```

## 🎉 **Hoàn thành:**

Công nợ thực tế đã được đơn giản hóa:
- ✅ **Bỏ điều kiện trạng thái** phức tạp
- ✅ **Công thức thuần túy:** `MAX(0, tong_tien - tong_da_tra - tien_coc)`
- ✅ **Áp dụng cho tất cả** đơn hàng
- ✅ **Logic đơn giản** và nhất quán
- ✅ **Phản ánh thực tế** tài chính

Bây giờ công nợ thực tế sẽ được tính theo công thức toán học thuần túy, không phụ thuộc vào trạng thái đơn hàng! 🎯

require('dotenv').config();
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, sequelize } = require('./models');

async function testActualDebtFormula() {
  try {
    console.log('🧪 Testing Actual Debt Formula: tiền SP - tiền đã trả - tiền cọc...\n');

    // 1. Tạo khách hàng test
    console.log('1️⃣ Creating test customer...');
    const customer = await NguoiDung.create({
      ho_ten: 'Nguyễn Văn Test Formula',
      so_dien_thoai: '0987654555',
      email: '<EMAIL>',
      loai_nguoi_dung: 'khach_hang',
      trang_thai: 'dang_giao_dich'
    });
    console.log(`✅ Created customer: ${customer.ho_ten} (ID: ${customer.id})`);

    // 2. Tạo đơn hàng test cases
    console.log('\n2️⃣ Creating test orders...');
    
    // Case 1: Đơn hàng hoàn thành, chưa thanh to<PERSON> gì
    const order1 = await <PERSON><PERSON>ang.create({
      ma_don_hang: `FORMULA-1-${Date.now()}`,
      khach_hang_id: customer.id,
      ngay_ban: new Date(),
      trang_thai: 'hoan_thanh', // Đã giao thành công
      tong_tien: 1000000, // Tiền sản phẩm
      tong_phai_tra: 500000, // Tiền COD
      tong_da_tra: 0, // Chưa thanh toán
      con_phai_tra: 300000, // Công nợ gốc
      tien_cod: 500000, // Tiền COD
      tien_coc: 200000, // Tiền cọc
      ghi_chu: 'Test case 1: Chưa thanh toán'
    });

    // Case 2: Đơn hàng hoàn thành, đã thanh toán một phần
    const order2 = await DonHang.create({
      ma_don_hang: `FORMULA-2-${Date.now()}`,
      khach_hang_id: customer.id,
      ngay_ban: new Date(),
      trang_thai: 'hoan_thanh', // Đã giao thành công
      tong_tien: 800000, // Tiền sản phẩm
      tong_phai_tra: 400000, // Tiền COD
      tong_da_tra: 200000, // Đã thanh toán một phần
      con_phai_tra: 100000, // Công nợ gốc
      tien_cod: 400000, // Tiền COD
      tien_coc: 200000, // Tiền cọc
      ghi_chu: 'Test case 2: Đã thanh toán một phần'
    });

    // Case 3: Đơn hàng hoàn thành, đã thanh toán đủ
    const order3 = await DonHang.create({
      ma_don_hang: `FORMULA-3-${Date.now()}`,
      khach_hang_id: customer.id,
      ngay_ban: new Date(),
      trang_thai: 'hoan_thanh', // Đã giao thành công
      tong_tien: 600000, // Tiền sản phẩm
      tong_phai_tra: 300000, // Tiền COD
      tong_da_tra: 300000, // Đã thanh toán đủ
      con_phai_tra: 0, // Công nợ gốc
      tien_cod: 300000, // Tiền COD
      tien_coc: 300000, // Tiền cọc
      ghi_chu: 'Test case 3: Đã thanh toán đủ'
    });

    // Case 4: Đơn hàng hoàn (không tính)
    const order4 = await DonHang.create({
      ma_don_hang: `FORMULA-4-${Date.now()}`,
      khach_hang_id: customer.id,
      ngay_ban: new Date(),
      trang_thai: 'hoan_hang', // Đã hoàn hàng
      tong_tien: 500000, // Tiền sản phẩm
      tong_phai_tra: 250000, // Tiền COD
      tong_da_tra: 0, // Chưa thanh toán
      con_phai_tra: 150000, // Công nợ gốc
      tien_cod: 250000, // Tiền COD
      tien_coc: 100000, // Tiền cọc
      ghi_chu: 'Test case 4: Đơn hoàn hàng'
    });

    console.log('✅ Created 4 test orders');

    // 3. Test công thức mới
    console.log('\n3️⃣ Testing new formula...');
    
    const orders = [order1, order2, order3, order4];
    const confirmedStatuses = ['da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh'];
    const completedStatuses = ['hoan_thanh'];
    const returnedStatuses = ['hoan_hang'];

    console.log('📊 Testing each order:');
    
    let totalActualDebt = 0;
    
    orders.forEach((order, index) => {
      console.log(`\n   Order ${index + 1}: ${order.ma_don_hang}`);
      console.log(`     - Status: ${order.trang_thai}`);
      console.log(`     - tong_tien: ${order.tong_tien?.toLocaleString('vi-VN')}đ`);
      console.log(`     - tong_da_tra: ${order.tong_da_tra?.toLocaleString('vi-VN')}đ`);
      console.log(`     - tien_coc: ${order.tien_coc?.toLocaleString('vi-VN')}đ`);
      
      let orderActualDebt = 0;
      
      if (returnedStatuses.includes(order.trang_thai)) {
        console.log(`     - Is Returned: YES → Actual Debt = 0đ`);
      } else if (completedStatuses.includes(order.trang_thai)) {
        const productAmount = parseFloat(order.tong_tien) || 0;
        const paidAmount = parseFloat(order.tong_da_tra) || 0;
        const depositAmount = parseFloat(order.tien_coc) || 0;
        orderActualDebt = Math.max(0, productAmount - paidAmount - depositAmount);
        
        console.log(`     - Formula: ${productAmount.toLocaleString('vi-VN')} - ${paidAmount.toLocaleString('vi-VN')} - ${depositAmount.toLocaleString('vi-VN')} = ${orderActualDebt.toLocaleString('vi-VN')}đ`);
        console.log(`     - Is Completed: YES → Actual Debt = ${orderActualDebt.toLocaleString('vi-VN')}đ`);
        
        totalActualDebt += orderActualDebt;
      } else {
        console.log(`     - Is Completed: NO → Not counted in actual debt`);
      }
    });

    console.log(`\n📈 Total Actual Debt: ${totalActualDebt.toLocaleString('vi-VN')}đ`);

    // 4. Verify expected results
    console.log('\n4️⃣ Verifying expected results...');
    
    const expectedResults = [
      { order: 1, expected: 1000000 - 0 - 200000, desc: "Chưa thanh toán" },
      { order: 2, expected: 800000 - 200000 - 200000, desc: "Thanh toán một phần" },
      { order: 3, expected: Math.max(0, 600000 - 300000 - 300000), desc: "Thanh toán đủ" },
      { order: 4, expected: 0, desc: "Đơn hoàn hàng" }
    ];

    let expectedTotal = 0;
    expectedResults.forEach((result, index) => {
      console.log(`   Order ${result.order} (${result.desc}): Expected = ${result.expected.toLocaleString('vi-VN')}đ`);
      if (index < 3) { // Chỉ tính 3 đơn đầu (đơn 4 là hoàn hàng)
        expectedTotal += result.expected;
      }
    });

    console.log(`   Expected Total: ${expectedTotal.toLocaleString('vi-VN')}đ`);

    // 5. Test API simulation
    console.log('\n5️⃣ Simulating API logic...');
    
    // Simulate getCustomerDebtDetail logic
    const customerOrders = await DonHang.findAll({
      where: { khach_hang_id: customer.id }
    });

    const apiActualDebt = customerOrders.reduce((sum, order) => {
      if (returnedStatuses.includes(order.trang_thai)) {
        return sum;
      }
      if (completedStatuses.includes(order.trang_thai)) {
        const productAmount = parseFloat(order.tong_tien) || 0;
        const paidAmount = parseFloat(order.tong_da_tra) || 0;
        const depositAmount = parseFloat(order.tien_coc) || 0;
        const orderActualDebt = productAmount - paidAmount - depositAmount;
        return sum + Math.max(0, orderActualDebt);
      }
      return sum;
    }, 0);

    console.log(`📡 API Calculated Actual Debt: ${apiActualDebt.toLocaleString('vi-VN')}đ`);

    // 6. Cleanup
    console.log('\n6️⃣ Cleaning up...');
    await order1.destroy();
    await order2.destroy();
    await order3.destroy();
    await order4.destroy();
    await customer.destroy();
    console.log('✅ Cleanup completed');

    // 7. Final verification
    console.log('\n7️⃣ Final Verification:');
    
    if (apiActualDebt === expectedTotal) {
      console.log('🎉 Formula test passed! New actual debt calculation works correctly.');
      console.log('✅ Formula: Tiền SP - Tiền đã trả - Tiền cọc');
      console.log('✅ Only counts completed orders');
      console.log('✅ Excludes returned orders');
      console.log('✅ Prevents negative debt');
    } else {
      console.log('❌ Formula test failed!');
      console.log(`   API Result: ${apiActualDebt.toLocaleString('vi-VN')}đ`);
      console.log(`   Expected: ${expectedTotal.toLocaleString('vi-VN')}đ`);
    }

    // 8. Show formula breakdown
    console.log('\n8️⃣ Formula Breakdown:');
    console.log('📋 New Actual Debt Formula:');
    console.log('   actual_debt = MAX(0, tong_tien - tong_da_tra - tien_coc)');
    console.log('   ');
    console.log('   Where:');
    console.log('   - tong_tien: Tiền sản phẩm trong đơn');
    console.log('   - tong_da_tra: Tiền đã thanh toán thực tế');
    console.log('   - tien_coc: Tiền cọc đã đặt');
    console.log('   - MAX(0, ...): Không cho phép công nợ âm');
    console.log('   ');
    console.log('   Conditions:');
    console.log('   - Only completed orders (hoan_thanh)');
    console.log('   - Exclude returned orders (hoan_hang)');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await sequelize.close();
  }
}

// Chạy test
testActualDebtFormula();

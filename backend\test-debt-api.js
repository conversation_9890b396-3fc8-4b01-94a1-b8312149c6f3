require("dotenv").config();
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Cong<PERSON>oNguoiDung, sequelize } = require("./models");

async function testDebtAPI() {
  try {
    console.log("🧪 Testing Debt API Logic...\n");

    // 1. Tạo khách hàng test
    console.log("1️⃣ Creating test customer...");
    const customer = await NguoiDung.create({
      ho_ten: "Nguyễn Văn Test API",
      so_dien_thoai: "0987654999",
      email: "<EMAIL>",
      loai_nguoi_dung: "khach_hang",
      trang_thai: "dang_giao_dich",
    });
    console.log(`✅ Created customer: ${customer.ho_ten} (ID: ${customer.id})`);

    // 2. Tạo đơn hàng test với logic mới
    console.log("\n2️⃣ Creating test order with new logic...");
    const order = await DonHang.create({
      ma_don_hang: `TEST-${Date.now()}`,
      khach_hang_id: customer.id,
      ngay_ban: new Date(),
      trang_thai: "da_xac_nhan",
      tong_tien: 1000000, // Tổng tiền sản phẩm
      tong_phai_tra: 500000, // Tiền COD
      tong_da_tra: 0, // Chưa thanh toán
      con_phai_tra: 300000, // Công nợ (1M - 500K - 200K cọc)
      tien_cod: 500000, // Tiền COD
      tien_coc: 200000, // Tiền cọc
      ghi_chu: "Test order for debt API",
    });
    console.log(`✅ Created order: ${order.ma_don_hang} (ID: ${order.id})`);

    // 3. Tạo thêm đơn hàng thứ 2 để test tổng
    console.log("\n3️⃣ Creating second test order...");
    const order2 = await DonHang.create({
      ma_don_hang: `TEST2-${Date.now()}`,
      khach_hang_id: customer.id,
      ngay_ban: new Date(),
      trang_thai: "da_xac_nhan",
      tong_tien: 800000, // Tổng tiền sản phẩm
      tong_phai_tra: 400000, // Tiền COD
      tong_da_tra: 0, // Chưa thanh toán
      con_phai_tra: 200000, // Công nợ (800K - 400K - 200K cọc)
      tien_cod: 400000, // Tiền COD
      tien_coc: 200000, // Tiền cọc
      ghi_chu: "Test order 2 for debt API",
    });
    console.log(`✅ Created order 2: ${order2.ma_don_hang} (ID: ${order2.id})`);

    // 4. Test query như API getDebtList (không cần CongNoNguoiDung nữa)
    console.log("\n4️⃣ Testing debt list query...");
    const customers = await NguoiDung.findAll({
      where: { id: customer.id },
      include: [
        {
          model: DonHang,
          as: "donHangKhachHang",
          attributes: [
            "id",
            "tong_tien", // Tổng tiền sản phẩm
            "tong_phai_tra", // Tiền COD
            "tong_da_tra", // Đã thanh toán
            "con_phai_tra", // Công nợ
            "tien_cod", // Tiền COD
            "tien_coc", // Tiền cọc
            "ngay_ban",
            "trang_thai",
          ],
          required: false,
        },
      ],
    });

    const testCustomer = customers[0];
    const orders = testCustomer.donHangKhachHang || [];

    // 5. Test logic tính toán (chỉ từ con_phai_tra)
    console.log("\n5️⃣ Testing calculation logic...");

    // Tổng công nợ trực tiếp từ con_phai_tra
    const totalDebt = orders.reduce((sum, order) => {
      return sum + (parseFloat(order.con_phai_tra) || 0);
    }, 0);

    // Tổng tiền mua (từ tong_tien)
    const totalPurchased = orders.reduce((sum, order) => {
      return sum + (order.tong_tien || 0);
    }, 0);

    // Công nợ quá hạn (từ con_phai_tra)
    const overdueDebt = orders.reduce((sum, order) => {
      const daysDiff = Math.floor(
        (new Date() - new Date(order.ngay_ban)) / (1000 * 60 * 60 * 24)
      );
      if (daysDiff > 30 && (order.con_phai_tra || 0) > 0) {
        return sum + (order.con_phai_tra || 0);
      }
      return sum;
    }, 0);

    console.log("📊 Calculation Results:");
    console.log(
      `   - Total Debt (from table): ${totalDebt.toLocaleString("vi-VN")} VND`
    );
    console.log(
      `   - Expected: ${(300000 + 200000).toLocaleString(
        "vi-VN"
      )} VND (300K + 200K)`
    );
    console.log(
      `   - Total Purchased: ${totalPurchased.toLocaleString("vi-VN")} VND`
    );
    console.log(
      `   - Expected: ${(1000000 + 800000).toLocaleString(
        "vi-VN"
      )} VND (1M + 800K)`
    );
    console.log(
      `   - Overdue Debt: ${overdueDebt.toLocaleString("vi-VN")} VND`
    );

    // 6. Test order data format
    console.log("\n6️⃣ Testing order data format...");
    const orderData = orders.map((order) => ({
      order_id: order.id,
      total_amount: order.tong_tien, // Tổng tiền sản phẩm
      cod_amount: order.tien_cod || 0, // Tiền COD
      paid_amount: order.tong_da_tra || 0, // Đã thanh toán
      debt_amount: order.con_phai_tra || 0, // Công nợ
      payment_status:
        (order.tong_da_tra || 0) >= (order.tien_cod || 0)
          ? "paid"
          : (order.tong_da_tra || 0) > 0
          ? "partial"
          : "unpaid",
    }));

    console.log("📋 Order Data Format:");
    orderData.forEach((order) => {
      console.log(`   Order ${order.order_id}:`);
      console.log(
        `     - Total Amount: ${order.total_amount.toLocaleString("vi-VN")} VND`
      );
      console.log(
        `     - COD Amount: ${order.cod_amount.toLocaleString("vi-VN")} VND`
      );
      console.log(
        `     - Paid Amount: ${order.paid_amount.toLocaleString("vi-VN")} VND`
      );
      console.log(
        `     - Debt Amount: ${order.debt_amount.toLocaleString("vi-VN")} VND`
      );
      console.log(`     - Payment Status: ${order.payment_status}`);
    });

    // 7. Cleanup
    console.log("\n7️⃣ Cleaning up...");
    await order.destroy();
    await order2.destroy();
    await customer.destroy();
    console.log("✅ Cleanup completed");

    console.log("\n🎉 All tests passed! Debt API logic is working correctly.");
  } catch (error) {
    console.error("❌ Test failed:", error);
  } finally {
    await sequelize.close();
  }
}

// Chạy test
testDebtAPI();

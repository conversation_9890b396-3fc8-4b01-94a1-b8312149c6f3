const { sequelize } = require('./models');

async function checkPaymentsTable() {
  try {
    console.log('🔍 Checking payments table...');
    
    // Check if table exists
    const [results] = await sequelize.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_TYPE = 'BASE TABLE' 
      AND TABLE_NAME = 'payments' 
      AND TABLE_SCHEMA = 'sale_system'
    `);
    
    console.log('Query results:', results);
    
    if (results.length > 0) {
      console.log('✅ Payments table exists');
      
      // Try to select from the table
      const [payments] = await sequelize.query('SELECT COUNT(*) as count FROM payments');
      console.log('📊 Payments count:', payments[0].count);
    } else {
      console.log('❌ Payments table does not exist');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error checking payments table:', error);
    process.exit(1);
  }
}

checkPaymentsTable();

import React, { useState } from "react";
import { Upload, Button, Image, message, Modal, Spin } from "antd";
import { PlusOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import { uploadAPI } from "../../services/api";

const ImageUpload = ({
  value = [],
  onChange,
  maxCount = 10,
  folder = "products",
  width = 800,
  height = 600,
}) => {
  const [uploading, setUploading] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState("");
  const [previewTitle, setPreviewTitle] = useState("");

  // Upload ảnh lên Cloudinary
  const uploadToCloudinary = async (file) => {
    const formData = new FormData();
    formData.append("image", file);
    formData.append("folder", folder);
    formData.append("width", width);
    formData.append("height", height);

    try {
      console.log("🚀 Starting upload to Cloudinary...", {
        fileName: file.name,
        fileSize: file.size,
        folder,
        width,
        height,
      });

      const response = await uploadAPI.uploadSimple(formData);
      const data = response.data;
      console.log("✅ Upload success:", data);

      if (data.success) {
        return {
          uid: data.data.public_id,
          name: file.name,
          status: "done",
          url: data.data.url,
          optimized_url: data.data.optimized_url,
          thumbnail_url: data.data.thumbnail_url,
          public_id: data.data.public_id,
        };
      } else {
        throw new Error(data.message || "Upload failed");
      }
    } catch (error) {
      console.error("❌ Upload error:", error);
      throw error;
    }
  };

  // Xử lý upload
  const handleUpload = async ({ file, onSuccess, onError }) => {
    console.log("🎯 handleUpload called with file:", {
      name: file.name,
      size: file.size,
      type: file.type,
      uid: file.uid,
    });

    try {
      setUploading(true);
      console.log("📤 Starting upload process...");

      const uploadedFile = await uploadToCloudinary(file);
      console.log("📥 Upload completed, updating file list...");

      const newFileList = [...value, uploadedFile];
      console.log("📋 New file list:", newFileList);

      if (onChange) {
        onChange(newFileList);
        console.log("✅ onChange called with new file list");
      } else {
        console.warn("⚠️ onChange prop is not provided!");
      }

      onSuccess(uploadedFile);
      message.success(`${file.name} uploaded successfully`);
    } catch (error) {
      console.error("❌ Upload failed in handleUpload:", error);
      onError(error);
      message.error(`${file.name} upload failed: ${error.message}`);
    } finally {
      setUploading(false);
      console.log("🏁 Upload process finished");
    }
  };

  // Xóa ảnh
  const handleRemove = (file) => {
    const newFileList = value.filter((item) => item.uid !== file.uid);
    onChange(newFileList);
  };

  // Preview ảnh
  const handlePreview = (file) => {
    setPreviewImage(file.url || file.preview);
    setPreviewVisible(true);
    setPreviewTitle(
      file.name || file.url.substring(file.url.lastIndexOf("/") + 1)
    );
  };

  // Đóng preview
  const handleCancel = () => setPreviewVisible(false);

  // Upload button
  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );

  return (
    <>
      <Upload
        listType="picture-card"
        fileList={value}
        customRequest={handleUpload}
        onRemove={handleRemove}
        onPreview={handlePreview}
        beforeUpload={(file) => {
          console.log("📁 File selected in beforeUpload:", {
            name: file.name,
            size: file.size,
            type: file.type,
          });

          const isImage = file.type.startsWith("image/");
          if (!isImage) {
            message.error("You can only upload image files!");
            return Upload.LIST_IGNORE;
          }
          const isLt5M = file.size / 1024 / 1024 < 5;
          if (!isLt5M) {
            message.error("Image must smaller than 5MB!");
            return Upload.LIST_IGNORE;
          }

          console.log("✅ File validation passed, proceeding with upload...");
          return true; // Allow upload to proceed to customRequest
        }}
        showUploadList={{
          showPreviewIcon: true,
          showRemoveIcon: true,
          showDownloadIcon: false,
        }}
      >
        {value.length >= maxCount ? null : uploadButton}
      </Upload>

      {uploading && (
        <div style={{ textAlign: "center", marginTop: 16 }}>
          <Spin /> Uploading...
        </div>
      )}

      <Modal
        open={previewVisible}
        title={previewTitle}
        footer={null}
        onCancel={handleCancel}
        width={800}
      >
        <img alt="preview" style={{ width: "100%" }} src={previewImage} />
      </Modal>
    </>
  );
};

export default ImageUpload;

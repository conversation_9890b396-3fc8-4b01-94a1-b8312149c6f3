const { sequelize } = require('./models');

async function testCustomerNames() {
  try {
    console.log('🔍 Testing customer names in business activity report...');
    
    // Test query để kiểm tra tên khách hàng
    const result = await sequelize.query(`
      SELECT
        nd.id as customer_id,
        nd.ho_ten as customer_name,
        nd.so_dien_thoai as customer_phone,
        nd.email as customer_email,
        nkh.ten_nhom as customer_group,
        nkh.ma_nhom as customer_group_code,
        COUNT(DISTINCT dh.id) as order_count,
        COALESCE(SUM(CASE WHEN dh.trang_thai != 'hoan_hang' THEN dh.tong_phai_tra ELSE 0 END), 0) as revenue

      FROM nguoi_dung nd
      LEFT JOIN don_hang dh ON nd.id = dh.khach_hang_id
        AND dh.ngay_ban BETWEEN DATE_SUB(NOW(), INTERVAL 30 DAY) AND NOW()
        AND dh.trang_thai IN ('da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh', 'hoan_hang')
      LEFT JOIN don_hang_san_pham dhsp ON dh.id = dhsp.don_hang_id
      LEFT JOIN phien_ban_san_pham pbsp ON dhsp.phien_ban_san_pham_id = pbsp.id
      LEFT JOIN nhom_khach_hang_nguoi_dung nkhnd ON nd.id = nkhnd.nguoi_dung_id
      LEFT JOIN nhom_khach_hang nkh ON nkhnd.nhom_khach_hang_id = nkh.id

      WHERE nd.loai_nguoi_dung = 'khach_hang'
      AND nd.trang_thai = 'dang_giao_dich'

      GROUP BY nd.id, nd.ho_ten, nd.so_dien_thoai, nd.email, nkh.ten_nhom, nkh.ma_nhom
      HAVING COUNT(DISTINCT dh.id) > 0
      ORDER BY revenue DESC
      LIMIT 10
    `, {
      type: sequelize.QueryTypes.SELECT
    });

    console.log('📊 Sample customer data:');
    result.forEach((customer, index) => {
      console.log(`${index + 1}. ID: ${customer.customer_id}`);
      console.log(`   Tên: "${customer.customer_name}"`);
      console.log(`   SĐT: ${customer.customer_phone}`);
      console.log(`   Email: ${customer.customer_email}`);
      console.log(`   Nhóm: ${customer.customer_group || 'Không có'}`);
      console.log(`   Đơn hàng: ${customer.order_count}`);
      console.log(`   Doanh thu: ${customer.revenue}`);
      console.log('   ---');
    });

    // Kiểm tra dữ liệu khách hàng
    const customerCheck = await sequelize.query(`
      SELECT id, ho_ten, so_dien_thoai, email, loai_nguoi_dung, trang_thai
      FROM nguoi_dung 
      WHERE loai_nguoi_dung = 'khach_hang' 
      AND trang_thai = 'dang_giao_dich'
      LIMIT 5
    `, {
      type: sequelize.QueryTypes.SELECT
    });

    console.log('\n👥 Sample customers in database:');
    customerCheck.forEach((customer, index) => {
      console.log(`${index + 1}. ID: ${customer.id}, Tên: "${customer.ho_ten}", SĐT: ${customer.so_dien_thoai}`);
    });

    // Kiểm tra đơn hàng
    const orderCheck = await sequelize.query(`
      SELECT dh.id, dh.khach_hang_id, nd.ho_ten, dh.tong_phai_tra, dh.trang_thai, dh.ngay_ban
      FROM don_hang dh
      LEFT JOIN nguoi_dung nd ON dh.khach_hang_id = nd.id
      WHERE dh.ngay_ban >= DATE_SUB(NOW(), INTERVAL 30 DAY)
      ORDER BY dh.ngay_ban DESC
      LIMIT 5
    `, {
      type: sequelize.QueryTypes.SELECT
    });

    console.log('\n📦 Sample orders:');
    orderCheck.forEach((order, index) => {
      console.log(`${index + 1}. Order ID: ${order.id}, Customer: "${order.ho_ten}", Amount: ${order.tong_phai_tra}, Status: ${order.trang_thai}`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await sequelize.close();
  }
}

testCustomerNames();
